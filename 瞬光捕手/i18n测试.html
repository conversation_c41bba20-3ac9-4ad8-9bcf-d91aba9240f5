<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瞬光捕手 - i18n测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 20px;
            margin: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            margin-bottom: 30px;
        }
        .test-controls {
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .pass { 
            border-left: 4px solid #00ff88;
            background: rgba(0, 255, 136, 0.1);
        }
        .fail { 
            border-left: 4px solid #ff4444;
            background: rgba(255, 68, 68, 0.1);
        }
        .log-output {
            background: rgba(0, 0, 0, 0.8);
            color: #00ff88;
            font-family: 'Consolas', 'Monaco', monospace;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }
        .demo-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }
        .demo-text {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
            border-left: 3px solid #00d4ff;
        }
        .language-switcher {
            text-align: center;
            margin: 20px 0;
        }
        .language-switcher select {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #00d4ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ 瞬光捕手 - 国际化服务测试</h1>
        
        <div class="test-controls">
            <button onclick="testI18nService()">🔍 测试 i18n 服务</button>
            <button onclick="testTranslations()">🌐 测试翻译功能</button>
            <button onclick="testLanguageSwitching()">🔄 测试语言切换</button>
            <button onclick="testDOMUpdates()">📄 测试 DOM 更新</button>
            <button onclick="clearResults()">🗑️ 清空结果</button>
        </div>

        <div class="language-switcher">
            <label>选择语言: </label>
            <select id="language-select" onchange="switchLanguage(this.value)">
                <option value="zh-CN">中文</option>
                <option value="en-US">English</option>
            </select>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-tests">0</div>
                <div>总测试数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passed-tests">0</div>
                <div>通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failed-tests">0</div>
                <div>失败测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="current-lang">-</div>
                <div>当前语言</div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎮 翻译演示</h3>
            <div class="demo-text" data-i18n="game.title">瞬光捕手</div>
            <div class="demo-text" data-i18n="game.subtitle">捕捉决定性瞬间，引燃无限可能</div>
            <div class="demo-text" data-i18n="menu.start">开始游戏</div>
            <div class="demo-text" data-i18n="menu.levelEditor">关卡编辑器</div>
            <div class="demo-text" data-i18n="settings.title">设置</div>
            <div class="demo-text" data-i18n="leaderboard.title">排行榜</div>
        </div>

        <div id="results"></div>
        <div id="logs" class="log-output" style="display: none;"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/storage-service.js"></script>
    <script src="js/utils/i18n.js"></script>

    <script>
        let logs = [];
        let testStats = { total: 0, passed: 0, failed: 0 };

        // 捕获控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            logs.push(`[LOG] ${new Date().toLocaleTimeString()} ${args.join(' ')}`);
            originalLog.apply(console, args);
            updateLogs();
        };

        console.error = function(...args) {
            logs.push(`[ERROR] ${new Date().toLocaleTimeString()} ${args.join(' ')}`);
            originalError.apply(console, args);
            updateLogs();
        };

        console.warn = function(...args) {
            logs.push(`[WARN] ${new Date().toLocaleTimeString()} ${args.join(' ')}`);
            originalWarn.apply(console, args);
            updateLogs();
        };

        function updateLogs() {
            const logsDiv = document.getElementById('logs');
            logsDiv.innerHTML = logs.slice(-30).join('\n');
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function addResult(name, passed, message) {
            testStats.total++;
            if (passed) {
                testStats.passed++;
            } else {
                testStats.failed++;
            }
            
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `<strong>${name}:</strong> ${message}`;
            document.getElementById('results').appendChild(div);
            
            updateStats();
        }

        function updateStats() {
            document.getElementById('total-tests').textContent = testStats.total;
            document.getElementById('passed-tests').textContent = testStats.passed;
            document.getElementById('failed-tests').textContent = testStats.failed;
            
            if (window.i18nService) {
                document.getElementById('current-lang').textContent = window.i18nService.getCurrentLanguage();
            }
        }

        function testI18nService() {
            clearResults();
            document.getElementById('logs').style.display = 'block';
            
            console.log('🔍 开始测试 i18n 服务基础功能...');
            
            // 测试1: i18nService 存在
            const serviceExists = typeof window.i18nService !== 'undefined';
            addResult('i18nService 存在', serviceExists, 
                serviceExists ? '✅ window.i18nService 已创建' : '❌ window.i18nService 不存在');
            
            if (!serviceExists) return;
            
            // 测试2: 核心方法存在
            const methods = ['t', 'setLanguage', 'getCurrentLanguage', 'getSupportedLanguages', 'applyTranslations'];
            methods.forEach(method => {
                const hasMethod = typeof window.i18nService[method] === 'function';
                addResult(`${method} 方法`, hasMethod, 
                    hasMethod ? `✅ ${method} 方法存在` : `❌ ${method} 方法不存在`);
            });
            
            // 测试3: 初始化状态
            const isInitialized = window.i18nService.initialized;
            addResult('初始化状态', isInitialized, 
                isInitialized ? '✅ 服务已初始化' : '❌ 服务未初始化');
            
            // 测试4: 支持的语言
            const supportedLangs = window.i18nService.getSupportedLanguages();
            const hasLanguages = Array.isArray(supportedLangs) && supportedLangs.length > 0;
            addResult('支持的语言', hasLanguages, 
                hasLanguages ? `✅ 支持 ${supportedLangs.length} 种语言: ${supportedLangs.map(l => l.name).join(', ')}` : '❌ 没有支持的语言');
        }

        function testTranslations() {
            console.log('🌐 开始测试翻译功能...');
            
            if (!window.i18nService) {
                addResult('翻译测试', false, '❌ i18nService 不存在');
                return;
            }
            
            // 测试翻译键
            const testKeys = [
                'game.title',
                'game.subtitle', 
                'menu.start',
                'menu.settings',
                'settings.language',
                'error.networkError'
            ];
            
            testKeys.forEach(key => {
                const translation = window.i18nService.t(key);
                const hasTranslation = translation && translation !== key;
                addResult(`翻译 ${key}`, hasTranslation, 
                    hasTranslation ? `✅ "${key}" -> "${translation}"` : `❌ "${key}" 翻译失败`);
            });
            
            // 测试参数化翻译
            const paramTest = window.i18nService.t('customLevels.confirmDelete', { name: '测试关卡' });
            const hasParams = paramTest.includes('测试关卡');
            addResult('参数化翻译', hasParams, 
                hasParams ? `✅ 参数化翻译成功: "${paramTest}"` : '❌ 参数化翻译失败');
        }

        async function testLanguageSwitching() {
            console.log('🔄 开始测试语言切换功能...');
            
            if (!window.i18nService) {
                addResult('语言切换测试', false, '❌ i18nService 不存在');
                return;
            }
            
            const originalLang = window.i18nService.getCurrentLanguage();
            const targetLang = originalLang === 'zh-CN' ? 'en-US' : 'zh-CN';
            
            try {
                // 切换语言
                await window.i18nService.setLanguage(targetLang);
                const newLang = window.i18nService.getCurrentLanguage();
                
                const switchSuccess = newLang === targetLang;
                addResult('语言切换', switchSuccess, 
                    switchSuccess ? `✅ 语言已切换: ${originalLang} -> ${newLang}` : `❌ 语言切换失败`);
                
                // 测试切换后的翻译
                const titleAfterSwitch = window.i18nService.t('game.title');
                const translationChanged = titleAfterSwitch !== '瞬光捕手' || targetLang === 'zh-CN';
                addResult('切换后翻译', translationChanged, 
                    translationChanged ? `✅ 翻译已更新: "${titleAfterSwitch}"` : '❌ 翻译未更新');
                
                // 切换回原语言
                await window.i18nService.setLanguage(originalLang);
                
            } catch (error) {
                addResult('语言切换', false, `❌ 语言切换异常: ${error.message}`);
            }
        }

        function testDOMUpdates() {
            console.log('📄 开始测试 DOM 更新功能...');
            
            if (!window.i18nService) {
                addResult('DOM 更新测试', false, '❌ i18nService 不存在');
                return;
            }
            
            // 应用翻译到演示区域
            window.i18nService.applyTranslations();
            
            // 检查演示区域的翻译是否正确应用
            const demoElements = document.querySelectorAll('.demo-text[data-i18n]');
            let updatedCount = 0;
            
            demoElements.forEach(element => {
                const key = element.getAttribute('data-i18n');
                const expectedText = window.i18nService.t(key);
                const actualText = element.textContent;
                
                if (actualText === expectedText) {
                    updatedCount++;
                }
            });
            
            const allUpdated = updatedCount === demoElements.length;
            addResult('DOM 翻译更新', allUpdated, 
                allUpdated ? `✅ ${updatedCount}/${demoElements.length} 个元素翻译正确` : 
                `❌ 只有 ${updatedCount}/${demoElements.length} 个元素翻译正确`);
        }

        async function switchLanguage(language) {
            if (window.i18nService) {
                await window.i18nService.setLanguage(language);
                updateStats();
                
                // 更新演示区域
                window.i18nService.applyTranslations();
                
                console.log(`🌍 语言已切换到: ${language}`);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('logs').style.display = 'none';
            logs = [];
            testStats = { total: 0, passed: 0, failed: 0 };
            updateStats();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 页面加载完成，初始化测试环境...');
            
            // 等待 i18n 服务初始化
            if (window.i18nService) {
                // 设置当前语言选择器
                const currentLang = window.i18nService.getCurrentLanguage();
                document.getElementById('language-select').value = currentLang;
                
                // 应用初始翻译
                window.i18nService.applyTranslations();
                updateStats();
                
                console.log('✅ 测试环境初始化完成');
            } else {
                console.error('❌ i18nService 未找到');
            }
        });
    </script>
</body>
</html>
